<script setup lang="ts">
import { h, computed } from "vue";
import { sortBy } from "lodash-es";
import Markdown from "@/components/Markdown.vue";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import type { Suggestion } from "../types";
import Button from "~/components/ui/button/Button.vue";
import { SquareArrowOutUpRightIcon } from "lucide-vue-next";

const currentThreadStore = useCurrentThreadStore();
const { suggestions, waitingForSuggestion } = storeToRefs(currentThreadStore);

const emit = defineEmits<{
  (e: "suggestion-clicked", suggestion: Suggestion): void;
}>();

const sortedSuggestions = computed(() => {
  if (!suggestions.value) return [];

  return sortBy(suggestions.value, (suggestion) =>
    suggestion.type === "link" ? 1 : 0
  );
});

const getComponent = (suggestion: Suggestion) => {
  if (suggestion.type === "text") {
    return () =>
      h(
        Button,
        {
          class:
            "block cursor-pointer bg-title-bg w-fit hover:bg-[#e5e5e5] h-fit flex justify-center items-center prose-p:text-deep-analysis-text px-3 py-1",
          onClick: () => emit("suggestion-clicked", suggestion),
        },
        {
          default: () =>
            h(Markdown, {
              source: suggestion.content,
              class:
                "!prose-sm prose-p:whitespace-normal text-left prose-p:text-xs",
            }),
        }
      );
  } else if (suggestion.type === "link") {
    return () =>
      h(
        Button,
        {
          class:
            "cursor-pointer hover:bg-[#e5e5e5] text-stone-700  whitespace-normal w-fit  h-fit px-3 py-1",
          variant: "secondary",
          as: "a",
          href: suggestion.link,
          target: "_blank",
        },
        {
          default: () => [
            h(
              "span",
              {
                class:
                  "whitespace-normal break-words text-deep-analysis-text text-xs",
              },
              suggestion.content.replace(/`/g, "")
            ),
            h(SquareArrowOutUpRightIcon, {
              size: 12,
            }),
          ],
        }
      );
  }
};
</script>

<template>
  <div
    v-if="!waitingForSuggestion && (suggestions?.length ?? 0) > 0"
    class="mt-6"
  >
    <!-- Related section title -->
    <div class="flex items-center gap-2 mb-4">
      <div class="flex items-center gap-1">
        <svg
          class="w-4 h-4 text-default-text"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <h3 class="text-sm font-medium text-default-text">Related</h3>
      </div>
    </div>

    <!-- Suggestions list -->
    <div class="text-sm text-deep-analysis-text">
      <component
        :is="getComponent(suggestion)"
        v-for="(suggestion, suggestionIndex) in sortedSuggestions"
        :key="suggestionIndex"
        v-motion="{
          initial: { opacity: 0 },
          enter: {
            opacity: 1,
            transition: {
              delay: suggestionIndex * 300,
            },
          },
        }"
        class="hover:shadow-lg mb-2.5"
      />
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.prose) {
  max-width: 100%;
}
</style>
